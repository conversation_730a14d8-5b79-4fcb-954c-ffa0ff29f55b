import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class AdvancedDropdown<T> extends StatefulWidget {
  const AdvancedDropdown({
    super.key,
    this.selectedValue,
    required this.onChanged,
    this.options = const [],
    this.hint,
    required this.itemToString,
    this.isExpanded = true,
    this.menuMaxHeight = 200,
    this.title,
    this.searchable = true,
    this.enabled = true,
    this.height = 48.0,
  });

  final T? selectedValue;
  final ValueChanged<T?> onChanged;
  final List<T> options;
  final String? hint;
  final String Function(T) itemToString;
  final bool isExpanded;
  final double menuMaxHeight;
  final String? title;
  final bool searchable;
  final bool enabled;
  final double height;

  @override
  State<AdvancedDropdown<T>> createState() => _AdvancedDropdownState<T>();
}

class _AdvancedDropdownState<T> extends State<AdvancedDropdown<T>> {
  final LayerLink _layerLink = LayerLink();
  final FocusNode _focusNode = FocusNode();
  final TextEditingController _searchController = TextEditingController();
  OverlayEntry? _overlayEntry;
  bool _isOpen = false;
  List<T> _filteredOptions = [];

  @override
  void initState() {
    super.initState();
    _filteredOptions = widget.options;
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChanged);
    _focusNode.dispose();
    _searchController.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus && _isOpen) {
      _closeDropdown();
    }
  }

  void _toggleDropdown() {
    if (!widget.enabled) return;

    if (_isOpen) {
      _closeDropdown();
    } else {
      _openDropdown();
    }
  }

  void _openDropdown() {
    _filteredOptions = widget.options;
    _searchController.clear();
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      _isOpen = true;
    });
  }

  void _closeDropdown() {
    _removeOverlay();
    setState(() {
      _isOpen = false;
    });
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _filterOptions(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredOptions = widget.options;
      } else {
        _filteredOptions = widget.options
            .where((option) => widget
                .itemToString(option)
                .toLowerCase()
                .contains(query.toLowerCase()))
            .toList();
      }
    });
    _overlayEntry?.markNeedsBuild();
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    return OverlayEntry(
      builder: (context) => Positioned(
        width: size.width,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0.0, size.height + 4.0),
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
            child: Container(
              constraints: BoxConstraints(
                maxHeight: widget.menuMaxHeight,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Palette.stroke),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (widget.searchable && _filteredOptions.length > 5) ...[
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextField(
                        controller: _searchController,
                        onChanged: _filterOptions,
                        decoration: InputDecoration(
                          hintText: 'Search...',
                          prefixIcon: const Icon(Icons.search, size: 20),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(color: Palette.stroke),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(color: Palette.stroke),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(6),
                            borderSide: BorderSide(color: Palette.primary),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          isDense: true,
                        ),
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                    Divider(height: 1, color: Palette.stroke),
                  ],
                  Flexible(
                    child: ListView.builder(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      itemCount: _filteredOptions.length,
                      itemBuilder: (context, index) {
                        final option = _filteredOptions[index];
                        final isSelected = widget.selectedValue == option;

                        return InkWell(
                          onTap: () {
                            widget.onChanged(option);
                            _closeDropdown();
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? Palette.primary.withValues(alpha: 0.1)
                                  : null,
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    widget.itemToString(option),
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(
                                          color: isSelected
                                              ? Palette.primary
                                              : Palette.primaryBlack,
                                          fontWeight: isSelected
                                              ? FontWeight.w600
                                              : FontWeight.w400,
                                        ),
                                  ),
                                ),
                                if (isSelected)
                                  Icon(
                                    Icons.check,
                                    size: 16,
                                    color: Palette.primary,
                                  ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return CompositedTransformTarget(
      link: _layerLink,
      child: Focus(
        focusNode: _focusNode,
        child: GestureDetector(
          onTap: _toggleDropdown,
          child: Container(
            height: widget.height,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              border: Border.all(
                color: _isOpen ? Palette.primary : Palette.stroke,
                width: _isOpen ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
              color: widget.enabled ? Colors.white : Palette.kF7F7F7,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.selectedValue != null
                        ? widget.itemToString(widget.selectedValue as T)
                        : widget.hint ?? 'Select an option',
                    style: textTheme.bodyMedium?.copyWith(
                      color: widget.selectedValue != null
                          ? Palette.primaryBlack
                          : Palette.placeholder,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Stacked chevron icons
                SizedBox(
                  width: 16,
                  height: 16,
                  child: Stack(
                    children: [
                      Positioned(
                        top: 0,
                        child: AnimatedRotation(
                          turns: _isOpen ? 0.5 : 0,
                          duration: const Duration(milliseconds: 200),
                          child: SvgPicture.asset(
                            '$kSvgDir/order/chevron_up.svg',
                            width: 12,
                            height: 6,
                            colorFilter: ColorFilter.mode(
                              widget.enabled
                                  ? Palette.primaryBlack
                                  : Palette.placeholder,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        child: AnimatedRotation(
                          turns: _isOpen ? 0.5 : 0,
                          duration: const Duration(milliseconds: 200),
                          child: SvgPicture.asset(
                            '$kSvgDir/order/chevron_down.svg',
                            width: 12,
                            height: 6,
                            colorFilter: ColorFilter.mode(
                              widget.enabled
                                  ? Palette.primaryBlack
                                  : Palette.placeholder,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
