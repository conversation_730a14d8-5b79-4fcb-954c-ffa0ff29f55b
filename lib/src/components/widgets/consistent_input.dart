import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class ConsistentInput extends StatelessWidget {
  const ConsistentInput({
    super.key,
    required this.controller,
    this.label,
    this.hint,
    this.prefix,
    this.suffix,
    this.prefixIcon,
    this.suffixIcon,
    this.keyboardType,
    this.inputFormatters,
    this.onEditingComplete,
    this.onChanged,
    this.onTap,
    this.maxLines = 1,
    this.minLines,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.validator,
    this.height = 48.0,
    this.borderRadius = 8.0,
    this.contentPadding,
    this.style,
    this.hintStyle,
    this.labelStyle,
    this.fillColor,
    this.borderColor,
    this.focusedBorderColor,
    this.errorBorderColor,
  });

  final TextEditingController controller;
  final String? label;
  final String? hint;
  final String? prefix;
  final String? suffix;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final int? maxLines;
  final int? minLines;
  final bool enabled;
  final bool readOnly;
  final bool autofocus;
  final String? Function(String?)? validator;
  final double height;
  final double borderRadius;
  final EdgeInsetsGeometry? contentPadding;
  final TextStyle? style;
  final TextStyle? hintStyle;
  final TextStyle? labelStyle;
  final Color? fillColor;
  final Color? borderColor;
  final Color? focusedBorderColor;
  final Color? errorBorderColor;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (label != null) ...[
          Text(
            label!,
            style: labelStyle ??
                textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: Palette.primaryBlack,
                ),
          ),
          const SizedBox(height: 8),
        ],
        Container(
          height: maxLines == 1 ? height : null,
          constraints: maxLines != 1 ? BoxConstraints(minHeight: height) : null,
          child: TextFormField(
            controller: controller,
            keyboardType: keyboardType,
            inputFormatters: inputFormatters,
            onEditingComplete: onEditingComplete,
            onChanged: onChanged,
            onTap: onTap,
            maxLines: maxLines,
            minLines: minLines,
            enabled: enabled,
            readOnly: readOnly,
            autofocus: autofocus,
            validator: validator,
            style: style ??
                textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w400,
                  color: enabled ? Palette.primaryBlack : Palette.placeholder,
                ),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: hintStyle ??
                  textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w400,
                    color: Palette.placeholder,
                  ),
              prefixText: prefix,
              suffixText: suffix,
              prefixIcon: prefixIcon,
              suffixIcon: suffixIcon,
              filled: true,
              fillColor:
                  fillColor ?? (enabled ? Colors.white : Palette.kF7F7F7),
              contentPadding: contentPadding ??
                  const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(borderRadius),
                borderSide: BorderSide(
                  color: borderColor ?? Palette.stroke,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(borderRadius),
                borderSide: BorderSide(
                  color: borderColor ?? Palette.stroke,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(borderRadius),
                borderSide: BorderSide(
                  color: focusedBorderColor ?? Palette.primary,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(borderRadius),
                borderSide: BorderSide(
                  color: errorBorderColor ?? Palette.kE61010,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(borderRadius),
                borderSide: BorderSide(
                  color: errorBorderColor ?? Palette.kE61010,
                  width: 2,
                ),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(borderRadius),
                borderSide: BorderSide(
                  color: Palette.stroke.withValues(alpha: 0.5),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class CompactInput extends StatelessWidget {
  const CompactInput({
    super.key,
    required this.controller,
    this.hint,
    this.prefix,
    this.suffix,
    this.keyboardType,
    this.inputFormatters,
    this.onEditingComplete,
    this.onChanged,
    this.autofocus = false,
    this.enabled = true,
    this.readOnly = false,
    this.height = 36.0,
    this.borderRadius = 6.0,
  });

  final TextEditingController controller;
  final String? hint;
  final String? prefix;
  final String? suffix;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onChanged;
  final bool autofocus;
  final bool enabled;
  final bool readOnly;
  final double height;
  final double borderRadius;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return SizedBox(
      height: height,
      child: TextField(
        controller: controller,
        keyboardType: keyboardType,
        inputFormatters: inputFormatters,
        onEditingComplete: onEditingComplete,
        onChanged: onChanged,
        autofocus: autofocus,
        enabled: enabled,
        readOnly: readOnly,
        style: textTheme.bodyMedium?.copyWith(
          fontSize: 13,
          fontWeight: FontWeight.w400,
          color: enabled ? Palette.primaryBlack : Palette.placeholder,
        ),
        decoration: InputDecoration(
          hintText: hint,
          hintStyle: textTheme.bodyMedium?.copyWith(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: Palette.placeholder,
          ),
          prefixText: prefix,
          suffixText: suffix,
          filled: true,
          fillColor: enabled ? Colors.white : Palette.kF7F7F7,
          isDense: true,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            borderSide: BorderSide(color: Palette.stroke),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            borderSide: BorderSide(color: Palette.stroke),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            borderSide: BorderSide(
              color: Palette.primary,
              width: 2,
            ),
          ),
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            borderSide: BorderSide(
              color: Palette.stroke.withValues(alpha: 0.5),
            ),
          ),
        ),
      ),
    );
  }
}
